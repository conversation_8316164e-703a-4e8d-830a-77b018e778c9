# Go build artifacts
*.exe
*.exe~
*.dll
*.so
*.dylib

# Go compiled binaries (project-specific)
main
server
nudgebot-api

# Go test artifacts
*.test
*.out
*.prof

# Go coverage files
coverage.txt
coverage.html
c.out

# Go workspace file
go.work
go.work.sum

# Dependency directories
vendor/

# Environment files
.env
.env.local
.env.test
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.settings/

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# Debug files
debug
debug.test

# Air live reload
.air.toml
tmp/

# Generated mocks (will be regenerated)
/mocks/*.go
!/mocks/generate.go

# Temporary documentation files (move to docs/ if needed)
*_SUMMARY.md
*_IMPLEMENTATION.md
*_ERROR_HANDLING.md
*progress*.md
*COMPLETION*.md
*failure*.md
demo_error_handling.go

# Local configuration files
configs/config.local.yaml
configs/config.*.yaml
!configs/config.yaml
!configs/config.example.yaml

# Test execution logs
test_execution_results.log
*.log
