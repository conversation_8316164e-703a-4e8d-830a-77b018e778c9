name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, "feature/*", "hotfix/*" ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:  # Allow manual triggers

env:
  GO_VERSION: '1.21'
  GOLANGCI_LINT_VERSION: 'v1.55'

jobs:
  # ==============================================================================
  # Code Quality and Security Checks
  # ==============================================================================
  
  quality-checks:
    name: 🔍 Code Quality & Security
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: 🐹 Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
        cache: true

    - name: 📦 Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/go/pkg/mod
          ~/.cache/go-build
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: 📥 Download dependencies
      run: make deps

    - name: 🎨 Check code formatting
      run: |
        make fmt
        if [ -n "$(git status --porcelain)" ]; then
          echo "❌ Code is not formatted. Run 'make fmt' locally."
          git diff
          exit 1
        fi

    - name: 🔍 Run golangci-lint
      uses: golangci/golangci-lint-action@v3
      with:
        version: ${{ env.GOLANGCI_LINT_VERSION }}
        args: --timeout=5m

    - name: 🔒 Run security audit
      run: |
        go install github.com/sonatypecommunity/nancy@latest
        make audit
      continue-on-error: true  # Don't fail CI for security warnings

    - name: 📋 Validate Go modules
      run: |
        make verify
        make tidy
        if [ -n "$(git status --porcelain)" ]; then
          echo "❌ Go modules are not tidy. Run 'make tidy' locally."
          git diff
          exit 1
        fi

  # ==============================================================================
  # Unit Tests
  # ==============================================================================
  
  unit-tests:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: quality-checks
    
    strategy:
      matrix:
        go-version: ['1.21', '1.22']
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐹 Set up Go ${{ matrix.go-version }}
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}
        cache: true

    - name: 📦 Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/go/pkg/mod
          ~/.cache/go-build
        key: ${{ runner.os }}-go-${{ matrix.go-version }}-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-${{ matrix.go-version }}-

    - name: 📥 Download dependencies
      run: make deps

    - name: 🔄 Generate mocks
      run: make generate-mocks

    - name: 🧪 Run unit tests with coverage
      run: make test-coverage

    - name: 📊 Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      if: matrix.go-version == '1.21'  # Only upload coverage once
      with:
        file: ./coverage.out
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

    - name: 📈 Generate coverage report
      if: matrix.go-version == '1.21'
      run: make test-coverage-html

    - name: 📤 Upload coverage report
      if: matrix.go-version == '1.21'
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: coverage.html
        retention-days: 30

  # ==============================================================================
  # Integration Tests
  # ==============================================================================
  
  integration-tests:
    name: 🔧 Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 25
    needs: quality-checks
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: test_nudgebot
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_pass
          POSTGRES_HOST_AUTH_METHOD: trust
        ports:
          - 5433:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7-alpine
        ports:
          - 6380:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 3s
          --health-retries 5

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐹 Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
        cache: true

    - name: 📦 Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/go/pkg/mod
          ~/.cache/go-build
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: 📥 Download dependencies
      run: make deps

    - name: 🔄 Generate mocks
      run: make generate-mocks

    - name: 🗄️ Wait for services
      run: |
        timeout 60 bash -c 'until nc -z localhost 5433; do sleep 1; done'
        timeout 60 bash -c 'until nc -z localhost 6380; do sleep 1; done'

    - name: 🔧 Run integration tests
      env:
        TEST_DB_HOST: localhost
        TEST_DB_PORT: 5433
        TEST_DB_USER: test_user
        TEST_DB_PASSWORD: test_pass
        TEST_DB_NAME: test_nudgebot
        TEST_REDIS_HOST: localhost
        TEST_REDIS_PORT: 6380
        ENV: test
      run: make test-integration

    - name: 🎯 Run essential tests
      env:
        TEST_DB_HOST: localhost
        TEST_DB_PORT: 5433
        TEST_DB_USER: test_user
        TEST_DB_PASSWORD: test_pass
        TEST_DB_NAME: test_nudgebot
        TEST_REDIS_HOST: localhost
        TEST_REDIS_PORT: 6380
        ENV: test
      run: make test-essential

  # ==============================================================================
  # Build and Deployment Tests
  # ==============================================================================
  
  build-tests:
    name: 🔨 Build Tests
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [unit-tests, integration-tests]
    
    strategy:
      matrix:
        platform: [linux/amd64, linux/arm64]
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐹 Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
        cache: true

    - name: 🔨 Build application
      env:
        GOOS: linux
        GOARCH: ${{ matrix.platform == 'linux/amd64' && 'amd64' || 'arm64' }}
      run: make build

    - name: 📤 Upload build artifact
      uses: actions/upload-artifact@v3
      with:
        name: nudgebot-api-${{ matrix.platform == 'linux/amd64' && 'amd64' || 'arm64' }}
        path: main
        retention-days: 7

  # ==============================================================================
  # Docker Build and Security Scan
  # ==============================================================================
  
  docker-tests:
    name: 🐳 Docker Build & Security
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [unit-tests, integration-tests]
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐳 Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: 🔨 Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: false
        tags: nudgebot-api:ci-test
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: 🔍 Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'nudgebot-api:ci-test'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: 📤 Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # ==============================================================================
  # Performance and Benchmark Tests
  # ==============================================================================
  
  performance-tests:
    name: 📊 Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: build-tests
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/main'
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐹 Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
        cache: true

    - name: 📦 Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/go/pkg/mod
          ~/.cache/go-build
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: 📥 Download dependencies
      run: make deps

    - name: 🏃 Run benchmarks
      run: make bench | tee benchmark.txt

    - name: 📤 Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: benchmark.txt
        retention-days: 30

  # ==============================================================================
  # Deployment Readiness Check
  # ==============================================================================
  
  deployment-check:
    name: 🚀 Deployment Readiness
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [build-tests, docker-tests]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: ✅ Deployment ready
      run: |
        echo "🎉 All checks passed! Ready for deployment."
        echo "📊 Summary:"
        echo "  ✅ Code quality checks passed"
        echo "  ✅ Unit tests passed"
        echo "  ✅ Integration tests passed"
        echo "  ✅ Build tests passed"
        echo "  ✅ Docker security scan completed"
        echo "🚀 Application is ready for deployment!"

  # ==============================================================================
  # Notification and Reporting
  # ==============================================================================
  
  notify:
    name: 📢 Notify Results
    runs-on: ubuntu-latest
    needs: [quality-checks, unit-tests, integration-tests, build-tests, docker-tests]
    if: always()
    
    steps:
    - name: 📢 Report CI Results
      run: |
        echo "🤖 NudgeBot API CI Pipeline Results"
        echo "=================================="
        echo "Quality Checks: ${{ needs.quality-checks.result }}"
        echo "Unit Tests: ${{ needs.unit-tests.result }}"
        echo "Integration Tests: ${{ needs.integration-tests.result }}"
        echo "Build Tests: ${{ needs.build-tests.result }}"
        echo "Docker Tests: ${{ needs.docker-tests.result }}"
        
        if [[ "${{ needs.quality-checks.result }}" == "success" && 
              "${{ needs.unit-tests.result }}" == "success" && 
              "${{ needs.integration-tests.result }}" == "success" && 
              "${{ needs.build-tests.result }}" == "success" && 
              "${{ needs.docker-tests.result }}" == "success" ]]; then
          echo "🎉 All CI checks passed!"
          exit 0
        else
          echo "❌ Some CI checks failed!"
          exit 1
        fi
