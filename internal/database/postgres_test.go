package database

import (
	"context"
	"fmt"
	"testing"
	"time"

	"nudgebot-api/internal/config"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

// TestHealthCheck_NilDatabase tests the HealthCheck function with nil database
func TestHealthCheck_NilDatabase(t *testing.T) {
	err := HealthCheck(nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database instance is nil")
}

// TestHealthCheck_UninitializedDatabase tests the HealthCheck function with uninitialized GORM DB
func TestHealthCheck_UninitializedDatabase(t *testing.T) {
	db := &gorm.DB{}
	err := HealthCheck(db)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not properly initialized")
}

// TestNewPostgresConnection_InvalidConfig tests connection with invalid configuration
func TestNewPostgresConnection_InvalidConfig(t *testing.T) {
	cfg := config.DatabaseConfig{
		Host:     "invalid-host-that-does-not-exist",
		Port:     9999,
		User:     "invalid_user",
		Password: "invalid_password",
		DBName:   "invalid_db",
		SSLMode:  "disable",
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: 300,
	}

	db, err := NewPostgresConnection(cfg)
	assert.Error(t, err)
	assert.Nil(t, db)
	assert.Contains(t, err.Error(), "failed to connect to database")
}

// TestNewPostgresConnection_ConfigValidation tests configuration validation
func TestNewPostgresConnection_ConfigValidation(t *testing.T) {
	tests := []struct {
		name    string
		config  config.DatabaseConfig
		wantErr bool
	}{
		{
			name: "Empty host",
			config: config.DatabaseConfig{
				Host:     "",
				Port:     5432,
				User:     "postgres",
				Password: "password",
				DBName:   "test",
				SSLMode:  "disable",
			},
			wantErr: true,
		},
		{
			name: "Invalid port",
			config: config.DatabaseConfig{
				Host:     "localhost",
				Port:     0,
				User:     "postgres",
				Password: "password",
				DBName:   "test",
				SSLMode:  "disable",
			},
			wantErr: true,
		},
		{
			name: "Empty database name",
			config: config.DatabaseConfig{
				Host:     "localhost",
				Port:     5432,
				User:     "postgres",
				Password: "password",
				DBName:   "",
				SSLMode:  "disable",
			},
			wantErr: true,
		},
		{
			name: "Valid config but unreachable database",
			config: config.DatabaseConfig{
				Host:     "localhost",
				Port:     5432,
				User:     "postgres",
				Password: "password",
				DBName:   "test",
				SSLMode:  "disable",
				MaxOpenConns:    10,
				MaxIdleConns:    5,
				ConnMaxLifetime: 300,
			},
			wantErr: true, // Will fail because no actual database is running
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, err := NewPostgresConnection(tt.config)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, db)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, db)
				if db != nil {
					sqlDB, _ := db.DB()
					if sqlDB != nil {
						sqlDB.Close()
					}
				}
			}
		})
	}
}

// TestNewPostgresConnection_ConnectionPoolConfiguration tests connection pool settings
func TestNewPostgresConnection_ConnectionPoolConfiguration(t *testing.T) {
	cfg := config.DatabaseConfig{
		Host:            "localhost",
		Port:            5432,
		User:            "postgres",
		Password:        "password",
		DBName:          "test",
		SSLMode:         "disable",
		MaxOpenConns:    25,
		MaxIdleConns:    10,
		ConnMaxLifetime: 600,
	}

	// This will fail to connect but we can test the configuration building
	_, err := NewPostgresConnection(cfg)
	assert.Error(t, err) // Expected since no database is actually running
	assert.Contains(t, err.Error(), "failed to connect to database")
}

// TestNewPostgresConnection_SSLModes tests different SSL modes
func TestNewPostgresConnection_SSLModes(t *testing.T) {
	sslModes := []string{"disable", "require", "verify-ca", "verify-full"}

	for _, sslMode := range sslModes {
		t.Run("SSLMode_"+sslMode, func(t *testing.T) {
			cfg := config.DatabaseConfig{
				Host:            "localhost",
				Port:            5432,
				User:            "postgres",
				Password:        "password",
				DBName:          "test",
				SSLMode:         sslMode,
				MaxOpenConns:    10,
				MaxIdleConns:    5,
				ConnMaxLifetime: 300,
			}

			_, err := NewPostgresConnection(cfg)
			assert.Error(t, err) // Expected since no database is running
			assert.Contains(t, err.Error(), "failed to connect to database")
		})
	}
}

// TestNewPostgresConnection_DSNBuilding tests DSN string construction
func TestNewPostgresConnection_DSNBuilding(t *testing.T) {
	cfg := config.DatabaseConfig{
		Host:     "test-host",
		Port:     5433,
		User:     "test_user",
		Password: "test_password",
		DBName:   "test_database",
		SSLMode:  "require",
	}

	// The DSN should be built correctly even if connection fails
	_, err := NewPostgresConnection(cfg)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to connect to database")
}

// TestHealthCheck_ErrorTypes tests different types of health check errors
func TestHealthCheck_ErrorTypes(t *testing.T) {
	tests := []struct {
		name        string
		setupDB     func() *gorm.DB
		expectError bool
		errorText   string
	}{
		{
			name: "Nil database",
			setupDB: func() *gorm.DB {
				return nil
			},
			expectError: true,
			errorText:   "database instance is nil",
		},
		{
			name: "Uninitialized GORM DB",
			setupDB: func() *gorm.DB {
				return &gorm.DB{}
			},
			expectError: true,
			errorText:   "not properly initialized",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := tt.setupDB()
			err := HealthCheck(db)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorText)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test struct for database operations
type TestModel struct {
	ID        uint `gorm:"primarykey"`
	Name      string
	CreatedAt time.Time
	UpdatedAt time.Time
}

// ==============================================================================
// Mock Database Tests for General Database Operation Patterns
// ==============================================================================
// Note: The following tests use a mock database to test general database
// operation patterns and behaviors without requiring a real PostgreSQL instance.
// These complement the above tests which test the actual PostgreSQL functions.

// Mock database for testing general database operation patterns
type mockDB struct {
	records map[uint]*TestModel
	nextID  uint
	closed  bool
}

func (m *mockDB) Create(record *TestModel) error {
	if m.closed {
		return fmt.Errorf("database connection closed")
	}
	if m.records == nil {
		m.records = make(map[uint]*TestModel)
	}
	m.nextID++
	record.ID = m.nextID
	record.CreatedAt = time.Now()
	record.UpdatedAt = time.Now()
	m.records[record.ID] = record
	return nil
}

func (m *mockDB) First(record *TestModel, id uint) error {
	if m.closed {
		return fmt.Errorf("database connection closed")
	}
	if found, exists := m.records[id]; exists {
		*record = *found
		return nil
	}
	return gorm.ErrRecordNotFound
}

func (m *mockDB) Update(id uint, field string, value interface{}) error {
	if m.closed {
		return fmt.Errorf("database connection closed")
	}
	if record, exists := m.records[id]; exists {
		if field == "Name" {
			record.Name = value.(string)
		}
		record.UpdatedAt = time.Now()
		return nil
	}
	return gorm.ErrRecordNotFound
}

func (m *mockDB) Delete(id uint) error {
	if m.closed {
		return fmt.Errorf("database connection closed")
	}
	if _, exists := m.records[id]; exists {
		delete(m.records, id)
		return nil
	}
	return gorm.ErrRecordNotFound
}

func (m *mockDB) Count() int64 {
	if m.closed {
		return 0
	}
	return int64(len(m.records))
}

func (m *mockDB) Close() error {
	m.closed = true
	return nil
}

func (m *mockDB) Ping() error {
	if m.closed {
		return fmt.Errorf("database connection closed")
	}
	return nil
}

func TestConnect_ValidConnection(t *testing.T) {
	db := &mockDB{}

	// Test that database is connected
	err := db.Ping()
	assert.NoError(t, err)

	// Clean up
	db.Close()
}

func TestConnect_DatabaseOperations(t *testing.T) {
	db := &mockDB{}
	defer db.Close()

	// Test Create
	testRecord := TestModel{Name: "Test Record"}
	err := db.Create(&testRecord)
	assert.NoError(t, err)
	assert.NotZero(t, testRecord.ID)

	// Test Read
	var retrieved TestModel
	err = db.First(&retrieved, testRecord.ID)
	assert.NoError(t, err)
	assert.Equal(t, "Test Record", retrieved.Name)

	// Test Update
	err = db.Update(retrieved.ID, "Name", "Updated Record")
	assert.NoError(t, err)

	// Verify update
	var updated TestModel
	err = db.First(&updated, testRecord.ID)
	assert.NoError(t, err)
	assert.Equal(t, "Updated Record", updated.Name)

	// Test Delete
	err = db.Delete(updated.ID)
	assert.NoError(t, err)

	// Verify deletion
	var deleted TestModel
	err = db.First(&deleted, testRecord.ID)
	assert.Error(t, err)
	assert.Equal(t, gorm.ErrRecordNotFound, err)
}

func TestConnect_TransactionSupport(t *testing.T) {
	db := &mockDB{}
	defer db.Close()

	// Test successful transaction simulation
	record1 := TestModel{Name: "Record 1"}
	err := db.Create(&record1)
	assert.NoError(t, err)

	record2 := TestModel{Name: "Record 2"}
	err = db.Create(&record2)
	assert.NoError(t, err)

	// Verify records were created
	count := db.Count()
	assert.Equal(t, int64(2), count)
}

func TestConnect_ConcurrentAccess(t *testing.T) {
	db := &mockDB{}
	defer db.Close()

	// Test concurrent writes
	const numGoroutines = 10
	done := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			record := TestModel{Name: fmt.Sprintf("Concurrent Record %d", id)}
			err := db.Create(&record)
			done <- err
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		err := <-done
		assert.NoError(t, err)
	}

	// Verify all records were created
	count := db.Count()
	assert.Equal(t, int64(numGoroutines), count)
}

func TestConnect_ContextTimeout(t *testing.T) {
	db := &mockDB{}
	defer db.Close()

	// Test with context timeout
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
	defer cancel()

	// Simulate a timeout by waiting
	select {
	case <-ctx.Done():
		assert.Error(t, ctx.Err())
		assert.Contains(t, ctx.Err().Error(), "context")
	case <-time.After(10 * time.Millisecond):
		t.Fatal("Expected context timeout")
	}
}

func TestConnect_ConnectionPooling(t *testing.T) {
	db := &mockDB{}
	defer db.Close()

	// Test multiple connections simulation
	const numConnections = 20
	done := make(chan error, numConnections)

	for i := 0; i < numConnections; i++ {
		go func() {
			err := db.Ping()
			done <- err
		}()
	}

	// Verify all connections worked
	for i := 0; i < numConnections; i++ {
		err := <-done
		assert.NoError(t, err)
	}
}

func TestConnect_PreparedStatements(t *testing.T) {
	db := &mockDB{}
	defer db.Close()

	// Create test data
	records := []TestModel{
		{Name: "Record 1"},
		{Name: "Record 2"},
		{Name: "Record 3"},
	}

	for _, record := range records {
		err := db.Create(&record)
		assert.NoError(t, err)
	}

	// Test finding specific records (simulated)
	count := db.Count()
	assert.Equal(t, int64(3), count)

	// Test finding record by ID
	var retrieved TestModel
	err := db.First(&retrieved, 2)
	assert.NoError(t, err)
	assert.Equal(t, "Record 2", retrieved.Name)
}

func TestConnect_ErrorHandling(t *testing.T) {
	db := &mockDB{}
	defer db.Close()

	tests := []struct {
		name          string
		operation     func() error
		expectError   bool
		errorContains string
	}{
		{
			name: "Select non-existent record",
			operation: func() error {
				var record TestModel
				return db.First(&record, 99999)
			},
			expectError:   true,
			errorContains: "record not found",
		},
		{
			name: "Valid ping",
			operation: func() error {
				return db.Ping()
			},
			expectError: false,
		},
		{
			name: "Closed database",
			operation: func() error {
				closedDB := &mockDB{}
				closedDB.Close()
				return closedDB.Ping()
			},
			expectError:   true,
			errorContains: "closed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.operation()

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestConnect_DatabaseStats(t *testing.T) {
	db := &mockDB{}
	defer db.Close()

	// Test basic database stats simulation
	err := db.Ping()
	assert.NoError(t, err)

	count := db.Count()
	assert.GreaterOrEqual(t, count, int64(0))
}

func TestConnect_Migrations(t *testing.T) {
	db := &mockDB{}
	defer db.Close()

	// Test migration simulation - create some test data
	record := TestModel{Name: "Migration Test"}
	err := db.Create(&record)
	assert.NoError(t, err)

	// Verify record exists
	var retrieved TestModel
	err = db.First(&retrieved, record.ID)
	assert.NoError(t, err)
	assert.Equal(t, "Migration Test", retrieved.Name)
}

func TestConnect_DatabaseInitialization(t *testing.T) {
	// Test database initialization patterns
	tests := []struct {
		name    string
		setup   func() *mockDB
		wantErr bool
	}{
		{
			name: "Normal initialization",
			setup: func() *mockDB {
				return &mockDB{}
			},
			wantErr: false,
		},
		{
			name: "Pre-closed database",
			setup: func() *mockDB {
				db := &mockDB{}
				db.Close()
				return db
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := tt.setup()
			defer db.Close()

			err := db.Ping()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestConnect_ConnectionLifecycle(t *testing.T) {
	db := &mockDB{}

	// Test connection lifecycle
	assert.False(t, db.closed)

	// Test operations before close
	err := db.Ping()
	assert.NoError(t, err)

	record := TestModel{Name: "Lifecycle Test"}
	err = db.Create(&record)
	assert.NoError(t, err)

	// Close connection
	err = db.Close()
	assert.NoError(t, err)
	assert.True(t, db.closed)

	// Test operations after close should fail
	err = db.Ping()
	assert.Error(t, err)

	record2 := TestModel{Name: "Should Fail"}
	err = db.Create(&record2)
	assert.Error(t, err)
}
