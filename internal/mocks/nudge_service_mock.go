// Code generated by MockGen. DO NOT EDIT.
// Source: ../nudge/service.go
//
// Generated by this command:
//
//	mockgen -source=../nudge/service.go -destination=./nudge_service_mock.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	common "nudgebot-api/internal/common"
	nudge "nudgebot-api/internal/nudge"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockNudgeService is a mock of NudgeService interface.
type MockNudgeService struct {
	ctrl     *gomock.Controller
	recorder *MockNudgeServiceMockRecorder
	isgomock struct{}
}

// MockNudgeServiceMockRecorder is the mock recorder for MockNudgeService.
type MockNudgeServiceMockRecorder struct {
	mock *MockNudgeService
}

// NewMockNudgeService creates a new mock instance.
func NewMockNudgeService(ctrl *gomock.Controller) *MockNudgeService {
	mock := &MockNudgeService{ctrl: ctrl}
	mock.recorder = &MockNudgeServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNudgeService) EXPECT() *MockNudgeServiceMockRecorder {
	return m.recorder
}

// BulkUpdateStatus mocks base method.
func (m *MockNudgeService) BulkUpdateStatus(taskIDs []common.TaskID, status common.TaskStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkUpdateStatus", taskIDs, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// BulkUpdateStatus indicates an expected call of BulkUpdateStatus.
func (mr *MockNudgeServiceMockRecorder) BulkUpdateStatus(taskIDs, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkUpdateStatus", reflect.TypeOf((*MockNudgeService)(nil).BulkUpdateStatus), taskIDs, status)
}

// CheckSubscriptionHealth mocks base method.
func (m *MockNudgeService) CheckSubscriptionHealth() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckSubscriptionHealth")
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckSubscriptionHealth indicates an expected call of CheckSubscriptionHealth.
func (mr *MockNudgeServiceMockRecorder) CheckSubscriptionHealth() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckSubscriptionHealth", reflect.TypeOf((*MockNudgeService)(nil).CheckSubscriptionHealth))
}

// CreateTask mocks base method.
func (m *MockNudgeService) CreateTask(task *nudge.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", task)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockNudgeServiceMockRecorder) CreateTask(task any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockNudgeService)(nil).CreateTask), task)
}

// DeleteTask mocks base method.
func (m *MockNudgeService) DeleteTask(taskID common.TaskID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTask", taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTask indicates an expected call of DeleteTask.
func (mr *MockNudgeServiceMockRecorder) DeleteTask(taskID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTask", reflect.TypeOf((*MockNudgeService)(nil).DeleteTask), taskID)
}

// GetNudgeSettings mocks base method.
func (m *MockNudgeService) GetNudgeSettings(userID common.UserID) (*nudge.NudgeSettings, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNudgeSettings", userID)
	ret0, _ := ret[0].(*nudge.NudgeSettings)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNudgeSettings indicates an expected call of GetNudgeSettings.
func (mr *MockNudgeServiceMockRecorder) GetNudgeSettings(userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNudgeSettings", reflect.TypeOf((*MockNudgeService)(nil).GetNudgeSettings), userID)
}

// GetOverdueTasks mocks base method.
func (m *MockNudgeService) GetOverdueTasks(userID common.UserID) ([]*nudge.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOverdueTasks", userID)
	ret0, _ := ret[0].([]*nudge.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOverdueTasks indicates an expected call of GetOverdueTasks.
func (mr *MockNudgeServiceMockRecorder) GetOverdueTasks(userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOverdueTasks", reflect.TypeOf((*MockNudgeService)(nil).GetOverdueTasks), userID)
}

// GetTaskStats mocks base method.
func (m *MockNudgeService) GetTaskStats(userID common.UserID) (*nudge.TaskStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskStats", userID)
	ret0, _ := ret[0].(*nudge.TaskStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskStats indicates an expected call of GetTaskStats.
func (mr *MockNudgeServiceMockRecorder) GetTaskStats(userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskStats", reflect.TypeOf((*MockNudgeService)(nil).GetTaskStats), userID)
}

// GetTasks mocks base method.
func (m *MockNudgeService) GetTasks(userID common.UserID, filter nudge.TaskFilter) ([]*nudge.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTasks", userID, filter)
	ret0, _ := ret[0].([]*nudge.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTasks indicates an expected call of GetTasks.
func (mr *MockNudgeServiceMockRecorder) GetTasks(userID, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTasks", reflect.TypeOf((*MockNudgeService)(nil).GetTasks), userID, filter)
}

// ScheduleReminder mocks base method.
func (m *MockNudgeService) ScheduleReminder(taskID common.TaskID, scheduledAt time.Time, reminderType nudge.ReminderType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScheduleReminder", taskID, scheduledAt, reminderType)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScheduleReminder indicates an expected call of ScheduleReminder.
func (mr *MockNudgeServiceMockRecorder) ScheduleReminder(taskID, scheduledAt, reminderType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScheduleReminder", reflect.TypeOf((*MockNudgeService)(nil).ScheduleReminder), taskID, scheduledAt, reminderType)
}

// SnoozeTask mocks base method.
func (m *MockNudgeService) SnoozeTask(taskID common.TaskID, snoozeUntil time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SnoozeTask", taskID, snoozeUntil)
	ret0, _ := ret[0].(error)
	return ret0
}

// SnoozeTask indicates an expected call of SnoozeTask.
func (mr *MockNudgeServiceMockRecorder) SnoozeTask(taskID, snoozeUntil any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SnoozeTask", reflect.TypeOf((*MockNudgeService)(nil).SnoozeTask), taskID, snoozeUntil)
}

// UpdateNudgeSettings mocks base method.
func (m *MockNudgeService) UpdateNudgeSettings(settings *nudge.NudgeSettings) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNudgeSettings", settings)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateNudgeSettings indicates an expected call of UpdateNudgeSettings.
func (mr *MockNudgeServiceMockRecorder) UpdateNudgeSettings(settings any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNudgeSettings", reflect.TypeOf((*MockNudgeService)(nil).UpdateNudgeSettings), settings)
}

// UpdateTaskStatus mocks base method.
func (m *MockNudgeService) UpdateTaskStatus(taskID common.TaskID, status common.TaskStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskStatus", taskID, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTaskStatus indicates an expected call of UpdateTaskStatus.
func (mr *MockNudgeServiceMockRecorder) UpdateTaskStatus(taskID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskStatus", reflect.TypeOf((*MockNudgeService)(nil).UpdateTaskStatus), taskID, status)
}
