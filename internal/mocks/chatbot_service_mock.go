// Code generated by MockGen. DO NOT EDIT.
// Source: ../chatbot/service.go
//
// Generated by this command:
//
//	mockgen -source=../chatbot/service.go -destination=./chatbot_service_mock.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	chatbot "nudgebot-api/internal/chatbot"
	common "nudgebot-api/internal/common"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockChatbotService is a mock of ChatbotService interface.
type MockChatbotService struct {
	ctrl     *gomock.Controller
	recorder *MockChatbotServiceMockRecorder
	isgomock struct{}
}

// MockChatbotServiceMockRecorder is the mock recorder for MockChatbotService.
type MockChatbotServiceMockRecorder struct {
	mock *MockChatbotService
}

// NewMockChatbotService creates a new mock instance.
func NewMockChatbotService(ctrl *gomock.Controller) *MockChatbotService {
	mock := &MockChatbotService{ctrl: ctrl}
	mock.recorder = &MockChatbotServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChatbotService) EXPECT() *MockChatbotServiceMockRecorder {
	return m.recorder
}

// HandleWebhook mocks base method.
func (m *MockChatbotService) HandleWebhook(webhookData []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleWebhook", webhookData)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleWebhook indicates an expected call of HandleWebhook.
func (mr *MockChatbotServiceMockRecorder) HandleWebhook(webhookData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleWebhook", reflect.TypeOf((*MockChatbotService)(nil).HandleWebhook), webhookData)
}

// ProcessCommand mocks base method.
func (m *MockChatbotService) ProcessCommand(command chatbot.Command, userID common.UserID, chatID common.ChatID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCommand", command, userID, chatID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ProcessCommand indicates an expected call of ProcessCommand.
func (mr *MockChatbotServiceMockRecorder) ProcessCommand(command, userID, chatID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCommand", reflect.TypeOf((*MockChatbotService)(nil).ProcessCommand), command, userID, chatID)
}

// SendMessage mocks base method.
func (m *MockChatbotService) SendMessage(chatID common.ChatID, text string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", chatID, text)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockChatbotServiceMockRecorder) SendMessage(chatID, text any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockChatbotService)(nil).SendMessage), chatID, text)
}

// SendMessageWithKeyboard mocks base method.
func (m *MockChatbotService) SendMessageWithKeyboard(chatID common.ChatID, text string, keyboard chatbot.InlineKeyboard) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessageWithKeyboard", chatID, text, keyboard)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMessageWithKeyboard indicates an expected call of SendMessageWithKeyboard.
func (mr *MockChatbotServiceMockRecorder) SendMessageWithKeyboard(chatID, text, keyboard any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessageWithKeyboard", reflect.TypeOf((*MockChatbotService)(nil).SendMessageWithKeyboard), chatID, text, keyboard)
}
