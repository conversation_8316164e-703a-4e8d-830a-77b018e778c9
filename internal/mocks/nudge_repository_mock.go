// Code generated by MockGen. DO NOT EDIT.
// Source: ../nudge/repository.go
//
// Generated by this command:
//
//	mockgen -source=../nudge/repository.go -destination=./nudge_repository_mock.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	common "nudgebot-api/internal/common"
	nudge "nudgebot-api/internal/nudge"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockTaskRepository is a mock of TaskRepository interface.
type MockTaskRepository struct {
	ctrl     *gomock.Controller
	recorder *MockTaskRepositoryMockRecorder
	isgomock struct{}
}

// MockTaskRepositoryMockRecorder is the mock recorder for MockTaskRepository.
type MockTaskRepositoryMockRecorder struct {
	mock *MockTaskRepository
}

// NewMockTaskRepository creates a new mock instance.
func NewMockTaskRepository(ctrl *gomock.Controller) *MockTaskRepository {
	mock := &MockTaskRepository{ctrl: ctrl}
	mock.recorder = &MockTaskRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskRepository) EXPECT() *MockTaskRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTaskRepository) Create(task *nudge.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", task)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockTaskRepositoryMockRecorder) Create(task any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTaskRepository)(nil).Create), task)
}

// Delete mocks base method.
func (m *MockTaskRepository) Delete(taskID common.TaskID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockTaskRepositoryMockRecorder) Delete(taskID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockTaskRepository)(nil).Delete), taskID)
}

// GetByID mocks base method.
func (m *MockTaskRepository) GetByID(taskID common.TaskID) (*nudge.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", taskID)
	ret0, _ := ret[0].(*nudge.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockTaskRepositoryMockRecorder) GetByID(taskID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockTaskRepository)(nil).GetByID), taskID)
}

// GetByUserID mocks base method.
func (m *MockTaskRepository) GetByUserID(userID common.UserID, filter nudge.TaskFilter) ([]*nudge.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByUserID", userID, filter)
	ret0, _ := ret[0].([]*nudge.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByUserID indicates an expected call of GetByUserID.
func (mr *MockTaskRepositoryMockRecorder) GetByUserID(userID, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByUserID", reflect.TypeOf((*MockTaskRepository)(nil).GetByUserID), userID, filter)
}

// GetStats mocks base method.
func (m *MockTaskRepository) GetStats(userID common.UserID) (*nudge.TaskStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStats", userID)
	ret0, _ := ret[0].(*nudge.TaskStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStats indicates an expected call of GetStats.
func (mr *MockTaskRepositoryMockRecorder) GetStats(userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStats", reflect.TypeOf((*MockTaskRepository)(nil).GetStats), userID)
}

// Update mocks base method.
func (m *MockTaskRepository) Update(task *nudge.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", task)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockTaskRepositoryMockRecorder) Update(task any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockTaskRepository)(nil).Update), task)
}

// MockReminderRepository is a mock of ReminderRepository interface.
type MockReminderRepository struct {
	ctrl     *gomock.Controller
	recorder *MockReminderRepositoryMockRecorder
	isgomock struct{}
}

// MockReminderRepositoryMockRecorder is the mock recorder for MockReminderRepository.
type MockReminderRepositoryMockRecorder struct {
	mock *MockReminderRepository
}

// NewMockReminderRepository creates a new mock instance.
func NewMockReminderRepository(ctrl *gomock.Controller) *MockReminderRepository {
	mock := &MockReminderRepository{ctrl: ctrl}
	mock.recorder = &MockReminderRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReminderRepository) EXPECT() *MockReminderRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockReminderRepository) Create(reminder *nudge.Reminder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", reminder)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockReminderRepositoryMockRecorder) Create(reminder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReminderRepository)(nil).Create), reminder)
}

// Delete mocks base method.
func (m *MockReminderRepository) Delete(reminderID common.ID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", reminderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockReminderRepositoryMockRecorder) Delete(reminderID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockReminderRepository)(nil).Delete), reminderID)
}

// GetByTaskID mocks base method.
func (m *MockReminderRepository) GetByTaskID(taskID common.TaskID) ([]*nudge.Reminder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTaskID", taskID)
	ret0, _ := ret[0].([]*nudge.Reminder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTaskID indicates an expected call of GetByTaskID.
func (mr *MockReminderRepositoryMockRecorder) GetByTaskID(taskID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTaskID", reflect.TypeOf((*MockReminderRepository)(nil).GetByTaskID), taskID)
}

// GetDueReminders mocks base method.
func (m *MockReminderRepository) GetDueReminders(before time.Time) ([]*nudge.Reminder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDueReminders", before)
	ret0, _ := ret[0].([]*nudge.Reminder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDueReminders indicates an expected call of GetDueReminders.
func (mr *MockReminderRepositoryMockRecorder) GetDueReminders(before any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDueReminders", reflect.TypeOf((*MockReminderRepository)(nil).GetDueReminders), before)
}

// MarkReminderSent mocks base method.
func (m *MockReminderRepository) MarkReminderSent(reminderID common.ID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkReminderSent", reminderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkReminderSent indicates an expected call of MarkReminderSent.
func (mr *MockReminderRepositoryMockRecorder) MarkReminderSent(reminderID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkReminderSent", reflect.TypeOf((*MockReminderRepository)(nil).MarkReminderSent), reminderID)
}

// MockNudgeSettingsRepository is a mock of NudgeSettingsRepository interface.
type MockNudgeSettingsRepository struct {
	ctrl     *gomock.Controller
	recorder *MockNudgeSettingsRepositoryMockRecorder
	isgomock struct{}
}

// MockNudgeSettingsRepositoryMockRecorder is the mock recorder for MockNudgeSettingsRepository.
type MockNudgeSettingsRepositoryMockRecorder struct {
	mock *MockNudgeSettingsRepository
}

// NewMockNudgeSettingsRepository creates a new mock instance.
func NewMockNudgeSettingsRepository(ctrl *gomock.Controller) *MockNudgeSettingsRepository {
	mock := &MockNudgeSettingsRepository{ctrl: ctrl}
	mock.recorder = &MockNudgeSettingsRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNudgeSettingsRepository) EXPECT() *MockNudgeSettingsRepositoryMockRecorder {
	return m.recorder
}

// CreateOrUpdate mocks base method.
func (m *MockNudgeSettingsRepository) CreateOrUpdate(settings *nudge.NudgeSettings) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdate", settings)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrUpdate indicates an expected call of CreateOrUpdate.
func (mr *MockNudgeSettingsRepositoryMockRecorder) CreateOrUpdate(settings any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdate", reflect.TypeOf((*MockNudgeSettingsRepository)(nil).CreateOrUpdate), settings)
}

// Delete mocks base method.
func (m *MockNudgeSettingsRepository) Delete(userID common.UserID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockNudgeSettingsRepositoryMockRecorder) Delete(userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockNudgeSettingsRepository)(nil).Delete), userID)
}

// GetByUserID mocks base method.
func (m *MockNudgeSettingsRepository) GetByUserID(userID common.UserID) (*nudge.NudgeSettings, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByUserID", userID)
	ret0, _ := ret[0].(*nudge.NudgeSettings)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByUserID indicates an expected call of GetByUserID.
func (mr *MockNudgeSettingsRepositoryMockRecorder) GetByUserID(userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByUserID", reflect.TypeOf((*MockNudgeSettingsRepository)(nil).GetByUserID), userID)
}

// MockNudgeRepository is a mock of NudgeRepository interface.
type MockNudgeRepository struct {
	ctrl     *gomock.Controller
	recorder *MockNudgeRepositoryMockRecorder
	isgomock struct{}
}

// MockNudgeRepositoryMockRecorder is the mock recorder for MockNudgeRepository.
type MockNudgeRepositoryMockRecorder struct {
	mock *MockNudgeRepository
}

// NewMockNudgeRepository creates a new mock instance.
func NewMockNudgeRepository(ctrl *gomock.Controller) *MockNudgeRepository {
	mock := &MockNudgeRepository{ctrl: ctrl}
	mock.recorder = &MockNudgeRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNudgeRepository) EXPECT() *MockNudgeRepositoryMockRecorder {
	return m.recorder
}

// CreateOrUpdateNudgeSettings mocks base method.
func (m *MockNudgeRepository) CreateOrUpdateNudgeSettings(settings *nudge.NudgeSettings) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateNudgeSettings", settings)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrUpdateNudgeSettings indicates an expected call of CreateOrUpdateNudgeSettings.
func (mr *MockNudgeRepositoryMockRecorder) CreateOrUpdateNudgeSettings(settings any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateNudgeSettings", reflect.TypeOf((*MockNudgeRepository)(nil).CreateOrUpdateNudgeSettings), settings)
}

// CreateReminder mocks base method.
func (m *MockNudgeRepository) CreateReminder(reminder *nudge.Reminder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateReminder", reminder)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateReminder indicates an expected call of CreateReminder.
func (mr *MockNudgeRepositoryMockRecorder) CreateReminder(reminder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateReminder", reflect.TypeOf((*MockNudgeRepository)(nil).CreateReminder), reminder)
}

// CreateTask mocks base method.
func (m *MockNudgeRepository) CreateTask(task *nudge.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", task)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockNudgeRepositoryMockRecorder) CreateTask(task any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockNudgeRepository)(nil).CreateTask), task)
}

// DeleteNudgeSettings mocks base method.
func (m *MockNudgeRepository) DeleteNudgeSettings(userID common.UserID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNudgeSettings", userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNudgeSettings indicates an expected call of DeleteNudgeSettings.
func (mr *MockNudgeRepositoryMockRecorder) DeleteNudgeSettings(userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNudgeSettings", reflect.TypeOf((*MockNudgeRepository)(nil).DeleteNudgeSettings), userID)
}

// DeleteReminder mocks base method.
func (m *MockNudgeRepository) DeleteReminder(reminderID common.ID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteReminder", reminderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteReminder indicates an expected call of DeleteReminder.
func (mr *MockNudgeRepositoryMockRecorder) DeleteReminder(reminderID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteReminder", reflect.TypeOf((*MockNudgeRepository)(nil).DeleteReminder), reminderID)
}

// DeleteTask mocks base method.
func (m *MockNudgeRepository) DeleteTask(taskID common.TaskID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTask", taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTask indicates an expected call of DeleteTask.
func (mr *MockNudgeRepositoryMockRecorder) DeleteTask(taskID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTask", reflect.TypeOf((*MockNudgeRepository)(nil).DeleteTask), taskID)
}

// GetDueReminders mocks base method.
func (m *MockNudgeRepository) GetDueReminders(before time.Time) ([]*nudge.Reminder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDueReminders", before)
	ret0, _ := ret[0].([]*nudge.Reminder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDueReminders indicates an expected call of GetDueReminders.
func (mr *MockNudgeRepositoryMockRecorder) GetDueReminders(before any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDueReminders", reflect.TypeOf((*MockNudgeRepository)(nil).GetDueReminders), before)
}

// GetNudgeSettingsByUserID mocks base method.
func (m *MockNudgeRepository) GetNudgeSettingsByUserID(userID common.UserID) (*nudge.NudgeSettings, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNudgeSettingsByUserID", userID)
	ret0, _ := ret[0].(*nudge.NudgeSettings)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNudgeSettingsByUserID indicates an expected call of GetNudgeSettingsByUserID.
func (mr *MockNudgeRepositoryMockRecorder) GetNudgeSettingsByUserID(userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNudgeSettingsByUserID", reflect.TypeOf((*MockNudgeRepository)(nil).GetNudgeSettingsByUserID), userID)
}

// GetRemindersByTaskID mocks base method.
func (m *MockNudgeRepository) GetRemindersByTaskID(taskID common.TaskID) ([]*nudge.Reminder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRemindersByTaskID", taskID)
	ret0, _ := ret[0].([]*nudge.Reminder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRemindersByTaskID indicates an expected call of GetRemindersByTaskID.
func (mr *MockNudgeRepositoryMockRecorder) GetRemindersByTaskID(taskID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRemindersByTaskID", reflect.TypeOf((*MockNudgeRepository)(nil).GetRemindersByTaskID), taskID)
}

// GetTaskByID mocks base method.
func (m *MockNudgeRepository) GetTaskByID(taskID common.TaskID) (*nudge.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskByID", taskID)
	ret0, _ := ret[0].(*nudge.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskByID indicates an expected call of GetTaskByID.
func (mr *MockNudgeRepositoryMockRecorder) GetTaskByID(taskID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskByID", reflect.TypeOf((*MockNudgeRepository)(nil).GetTaskByID), taskID)
}

// GetTaskStats mocks base method.
func (m *MockNudgeRepository) GetTaskStats(userID common.UserID) (*nudge.TaskStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskStats", userID)
	ret0, _ := ret[0].(*nudge.TaskStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskStats indicates an expected call of GetTaskStats.
func (mr *MockNudgeRepositoryMockRecorder) GetTaskStats(userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskStats", reflect.TypeOf((*MockNudgeRepository)(nil).GetTaskStats), userID)
}

// GetTasksByUserID mocks base method.
func (m *MockNudgeRepository) GetTasksByUserID(userID common.UserID, filter nudge.TaskFilter) ([]*nudge.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTasksByUserID", userID, filter)
	ret0, _ := ret[0].([]*nudge.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTasksByUserID indicates an expected call of GetTasksByUserID.
func (mr *MockNudgeRepositoryMockRecorder) GetTasksByUserID(userID, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTasksByUserID", reflect.TypeOf((*MockNudgeRepository)(nil).GetTasksByUserID), userID, filter)
}

// MarkReminderSent mocks base method.
func (m *MockNudgeRepository) MarkReminderSent(reminderID common.ID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkReminderSent", reminderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkReminderSent indicates an expected call of MarkReminderSent.
func (mr *MockNudgeRepositoryMockRecorder) MarkReminderSent(reminderID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkReminderSent", reflect.TypeOf((*MockNudgeRepository)(nil).MarkReminderSent), reminderID)
}

// UpdateTask mocks base method.
func (m *MockNudgeRepository) UpdateTask(task *nudge.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTask", task)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTask indicates an expected call of UpdateTask.
func (mr *MockNudgeRepositoryMockRecorder) UpdateTask(task any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTask", reflect.TypeOf((*MockNudgeRepository)(nil).UpdateTask), task)
}

// WithTransaction mocks base method.
func (m *MockNudgeRepository) WithTransaction(fn func(nudge.NudgeRepository) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTransaction", fn)
	ret0, _ := ret[0].(error)
	return ret0
}

// WithTransaction indicates an expected call of WithTransaction.
func (mr *MockNudgeRepositoryMockRecorder) WithTransaction(fn any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTransaction", reflect.TypeOf((*MockNudgeRepository)(nil).WithTransaction), fn)
}
