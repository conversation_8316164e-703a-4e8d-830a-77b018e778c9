// Code generated by MockGen. DO NOT EDIT.
// Source: ../llm/service.go
//
// Generated by this command:
//
//	mockgen -source=../llm/service.go -destination=./llm_service_mock.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	common "nudgebot-api/internal/common"
	llm "nudgebot-api/internal/llm"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockLLMService is a mock of LLMService interface.
type MockLLMService struct {
	ctrl     *gomock.Controller
	recorder *MockLLMServiceMockRecorder
	isgomock struct{}
}

// MockLLMServiceMockRecorder is the mock recorder for MockLLMService.
type MockLLMServiceMockRecorder struct {
	mock *MockLLMService
}

// NewMockLLMService creates a new mock instance.
func NewMockLLMService(ctrl *gomock.Controller) *MockLLMService {
	mock := &MockLLMService{ctrl: ctrl}
	mock.recorder = &MockLLMServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLLMService) EXPECT() *MockLLMServiceMockRecorder {
	return m.recorder
}

// GetSuggestions mocks base method.
func (m *MockLLMService) GetSuggestions(partialText string, userID common.UserID) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSuggestions", partialText, userID)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSuggestions indicates an expected call of GetSuggestions.
func (mr *MockLLMServiceMockRecorder) GetSuggestions(partialText, userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuggestions", reflect.TypeOf((*MockLLMService)(nil).GetSuggestions), partialText, userID)
}

// ParseTask mocks base method.
func (m *MockLLMService) ParseTask(text string, userID common.UserID) (*llm.LLMResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseTask", text, userID)
	ret0, _ := ret[0].(*llm.LLMResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseTask indicates an expected call of ParseTask.
func (mr *MockLLMServiceMockRecorder) ParseTask(text, userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseTask", reflect.TypeOf((*MockLLMService)(nil).ParseTask), text, userID)
}

// ValidateTask mocks base method.
func (m *MockLLMService) ValidateTask(parsedTask llm.ParsedTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateTask", parsedTask)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateTask indicates an expected call of ValidateTask.
func (mr *MockLLMServiceMockRecorder) ValidateTask(parsedTask any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateTask", reflect.TypeOf((*MockLLMService)(nil).ValidateTask), parsedTask)
}
