// Code generated by MockGen. DO NOT EDIT.
// Source: ../events/bus.go
//
// Generated by this command:
//
//	mockgen -source=../events/bus.go -destination=./event_bus_mock.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockEventBus is a mock of EventBus interface.
type MockEventBus struct {
	ctrl     *gomock.Controller
	recorder *MockEventBusMockRecorder
	isgomock struct{}
}

// MockEventBusMockRecorder is the mock recorder for MockEventBus.
type MockEventBusMockRecorder struct {
	mock *MockEventBus
}

// NewMockEventBus creates a new mock instance.
func NewMockEventBus(ctrl *gomock.Controller) *MockEventBus {
	mock := &MockEventBus{ctrl: ctrl}
	mock.recorder = &MockEventBusMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventBus) EXPECT() *MockEventBusMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockEventBus) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockEventBusMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockEventBus)(nil).Close))
}

// Publish mocks base method.
func (m *MockEventBus) Publish(topic string, data any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", topic, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockEventBusMockRecorder) Publish(topic, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockEventBus)(nil).Publish), topic, data)
}

// Subscribe mocks base method.
func (m *MockEventBus) Subscribe(topic string, handler any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Subscribe", topic, handler)
	ret0, _ := ret[0].(error)
	return ret0
}

// Subscribe indicates an expected call of Subscribe.
func (mr *MockEventBusMockRecorder) Subscribe(topic, handler any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Subscribe", reflect.TypeOf((*MockEventBus)(nil).Subscribe), topic, handler)
}

// Unsubscribe mocks base method.
func (m *MockEventBus) Unsubscribe(topic string, handler any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unsubscribe", topic, handler)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unsubscribe indicates an expected call of Unsubscribe.
func (mr *MockEventBusMockRecorder) Unsubscribe(topic, handler any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unsubscribe", reflect.TypeOf((*MockEventBus)(nil).Unsubscribe), topic, handler)
}
