// Code generated by MockGen. DO NOT EDIT.
// Source: ../llm/provider.go
//
// Generated by this command:
//
//	mockgen -source=../llm/provider.go -destination=./llm_provider_mock.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	llm "nudgebot-api/internal/llm"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockLLMProvider is a mock of LLMProvider interface.
type MockLLMProvider struct {
	ctrl     *gomock.Controller
	recorder *MockLLMProviderMockRecorder
	isgomock struct{}
}

// MockLLMProviderMockRecorder is the mock recorder for MockLLMProvider.
type MockLLMProviderMockRecorder struct {
	mock *MockLLMProvider
}

// NewMockLLMProvider creates a new mock instance.
func NewMockLLMProvider(ctrl *gomock.Controller) *MockLLMProvider {
	mock := &MockLLMProvider{ctrl: ctrl}
	mock.recorder = &MockLLMProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLLMProvider) EXPECT() *MockLLMProviderMockRecorder {
	return m.recorder
}

// GetModelInfo mocks base method.
func (m *MockLLMProvider) GetModelInfo() llm.ModelInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetModelInfo")
	ret0, _ := ret[0].(llm.ModelInfo)
	return ret0
}

// GetModelInfo indicates an expected call of GetModelInfo.
func (mr *MockLLMProviderMockRecorder) GetModelInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetModelInfo", reflect.TypeOf((*MockLLMProvider)(nil).GetModelInfo))
}

// ParseTask mocks base method.
func (m *MockLLMProvider) ParseTask(ctx context.Context, req llm.ParseRequest) (*llm.LLMResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseTask", ctx, req)
	ret0, _ := ret[0].(*llm.LLMResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseTask indicates an expected call of ParseTask.
func (mr *MockLLMProviderMockRecorder) ParseTask(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseTask", reflect.TypeOf((*MockLLMProvider)(nil).ParseTask), ctx, req)
}

// ValidateConnection mocks base method.
func (m *MockLLMProvider) ValidateConnection(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateConnection", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateConnection indicates an expected call of ValidateConnection.
func (mr *MockLLMProviderMockRecorder) ValidateConnection(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateConnection", reflect.TypeOf((*MockLLMProvider)(nil).ValidateConnection), ctx)
}
