# Server Configuration
SERVER_PORT=8080
SERVER_ENVIRONMENT=development
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres
DATABASE_DBNAME=nudgebot
DATABASE_SSLMODE=disable
DATABASE_MAX_OPEN_CONNS=25
DATABASE_MAX_IDLE_CONNS=5
DATABASE_CONN_MAX_LIFETIME=300

# Chatbot Configuration
CHATBOT_WEBHOOK_URL=/api/v1/telegram/webhook
CHATBOT_TOKEN=your_telegram_bot_token_here
CHATBOT_TIMEOUT=30

# LLM Configuration
LLM_API_ENDPOINT=https://generativelanguage.googleapis.com/v1beta/models/gemma-2-27b-it:generateContent
LLM_API_KEY=your_gemini_api_key_here
LLM_TIMEOUT=30
LLM_MAX_RETRIES=3
LLM_MODEL=gemma-2-27b-it

# Events Configuration
EVENTS_BUFFER_SIZE=1000
EVENTS_WORKER_COUNT=4
EVENTS_SHUTDOWN_TIMEOUT=30

# Nudge Configuration
NUDGE_DEFAULT_REMINDER_INTERVAL=3600
NUDGE_MAX_NUDGES=3
NUDGE_CLEANUP_INTERVAL=86400

# Scheduler Configuration
SCHEDULER_ENABLED=true
SCHEDULER_POLL_INTERVAL=30
SCHEDULER_NUDGE_DELAY=7200
SCHEDULER_WORKER_COUNT=2
SCHEDULER_SHUTDOWN_TIMEOUT=30